plugins {
	id 'org.springframework.boot' version '2.5.4'
	id 'io.spring.dependency-management' version '1.0.11.RELEASE'
	id 'java'
	id 'war'
}

group = 'com.nimble'
sourceCompatibility = '1.8'

repositories {
	mavenCentral()
}
war {
   	archiveName = 'freshdeskhooks.war'
	enabled = true
    baseName = 'freshdeskhooks'
}
dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-freemarker'
	
	providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	
	implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.33'
	implementation 'org.hibernate:hibernate-core:5.5.0.Final'
	implementation group: 'org.json', name: 'json', version: '20090211'
	implementation group: 'com.mchange', name: 'c3p0', version: '0.9.5.5'
	implementation group: 'org.springframework', name: 'spring-orm', version: '4.3.17.RELEASE'
  	
  	implementation group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.17.0'
    implementation group: 'org.apache.logging.log4j', name: 'log4j-core', version: '2.17.0'
    implementation group: 'org.apache.logging.log4j', name: 'log4j-web', version: '2.17.0'
    
    implementation group: 'com.squareup.okhttp', name: 'okhttp', version: '2.7.5'
    implementation group: 'com.sun.mail', name: 'javax.mail', version: '1.6.2'
}
configurations.all {
 exclude module: 'log4j-to-slf4j'
 exclude module: 'slf4j-log4j12'
}
test {
    exclude '**/*'
}
