package com.nimble.webhooks.dto;

//import java.sql.Timestamp;
//import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

public class JResponse {

	Map<String, Object> response;

//	private Timestamp orderDetails;

	public JResponse() {
		super();
		response = new HashMap<String, Object>();
		// TODO Auto-generated constructor stub
	}

	/*
	 * public JResponse(Map<String, Map<String, Object>> response) { super();
	 * this.response = response; }
	 */
	public Map<String, Object> getResponse() {
		return response;
	}

	public void setResponse(Map<String, Object> response) {
		this.response = response;
	}

	public void put(String string, Object success) {

		response.put(string, success);
	}

	public void remove(String key) {
		response.remove(key);
	}

	public Object get(String key) {
		return response.get(key);
	}

}
