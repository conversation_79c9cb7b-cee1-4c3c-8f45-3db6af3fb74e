package com.nimble.webhooks.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Base64;
import java.util.Enumeration;
import java.util.HashMap;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.nimble.webhooks.dto.JResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.nimble.webhooks.helper.Helper;
import com.nimble.webhooks.irisentity.FreshDeskHooks;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IDataBaseService;

@RestController
public class FreshdeskWebhooks {

	@Value("${validation_authkey}")
	private String validation_authkey;

	@Autowired
	IAsyncService async;

	@Autowired
	IDataBaseService dataBaseService;

	private static final Logger log = LogManager.getLogger(FreshdeskWebhooks.class);

	@PostMapping(value = "/v1.0/freshdesk/tickets", headers = "Accept=application/json")
	@ResponseBody
	public ResponseEntity<?> freshDeskwebhooks(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		log.info("Entered freshDeskwebhooks ::");
		boolean iserroneous = true;
		try {

			BufferedReader rd = request.getReader();
			StringBuffer result = new StringBuffer();
			String line = "";

			while ((line = rd.readLine()) != null) {
				result.append(line);
			}

			try {
				HashMap<String, String> map = new HashMap<String, String>();
				Enumeration headerNames = request.getHeaderNames();
				while (headerNames.hasMoreElements()) {
					String key = (String) headerNames.nextElement();
					String value = request.getHeader(key);
					map.put(key, value);
				}

				String values = (String) map.get("authorization");
				byte[] authEncBytes = Base64.getDecoder().decode(values.split(" ")[1].getBytes("UTF-8"));
				String credentials = new String(authEncBytes, "UTF-8");
				credentials = credentials.split("\\:")[0];
				if (!credentials.equals(validation_authkey)) {
					JResponse response1 = new JResponse();
					response1.put("message", "Invalid Credentials");
					response1.put("status", "Error");
					response1.put("statusCode", 401);
					return ResponseEntity
							.status(HttpStatus.UNAUTHORIZED)
							.contentType(MediaType.APPLICATION_JSON)
							.body(response1);
				}

			} catch (Exception e) {
				JResponse response1 = new JResponse();
				response1.put("message", "Error in processing request");
				response1.put("status", "Error");
				response1.put("statusCode", 500);
				return ResponseEntity.
						status(HttpStatus.BAD_REQUEST)
						.contentType(MediaType.APPLICATION_JSON)
						.body(response1);
			}

			rd.close();

			JSONObject resp = new JSONObject(result.toString());
			log.info("Freshdesk request body data : " + resp);
			JSONObject res = resp.getJSONObject("freshdesk_webhook");

			String ticket_id = res.getString("ticket_id");
			String triggered_event = res.getString("triggered_event");

			log.info("Ticket Id : " + ticket_id + ", Triggered Event : " + triggered_event);

			FreshDeskHooks freshDeskHooks = dataBaseService.webHookStatusIsAvailable(ticket_id);
			if (freshDeskHooks != null && freshDeskHooks.getTriggered_event().equalsIgnoreCase(triggered_event)
					&& freshDeskHooks.isUpdate_status()) {
				log.error("ticket_number already updated in webhooks_status history Table. : " + ticket_id);
				JResponse response1 = new JResponse();
				response1.put("status", "Success");
				response1.put("message", "Already available");
				response1.put("statusCode", 200);
				return ResponseEntity
						.status(HttpStatus.OK)
						.contentType(MediaType.APPLICATION_JSON)
						.body(response1);
			}
			
			freshDeskHooks = Helper.setData(freshDeskHooks, res);
			freshDeskHooks = dataBaseService.saveFreshDeshHooks(freshDeskHooks);

			if (result != null) {
				async.ticketProcessor(result.toString(), freshDeskHooks);
			}

			JResponse response1 = new JResponse();
			response1.put("status", "Success");
			response1.put("message", "Webhook received successfully");
			response1.put("ticket_id", ticket_id);
			response1.put("statusCode", 200);
			return ResponseEntity
					.status(HttpStatus.OK)
					.contentType(MediaType.APPLICATION_JSON)
					.body(response1);
		} catch (Exception e) {
			log.error("Error in freshDeskwebhooks : {}", e.getLocalizedMessage());
			JResponse response1 = new JResponse();
			response1.put("status", "Error");
			response1.put("message", "Unable to process request");
			response1.put("error", "Internal Server Error");
			response1.put("statusCode", 500);
			return ResponseEntity
					.status(HttpStatus.BAD_REQUEST)
					.contentType(MediaType.APPLICATION_JSON)
					.body(response1);
		}
	}
}
