package com.nimble.webhooks;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAutoConfiguration(exclude = HibernateJpaAutoConfiguration.class)
@ComponentScan({ "com.nimble.webhooks", "com.nimble.webhooks.controller", "com.nimble.webhooks.dao",
		"com.nimble.webhooks.service", "com.nimble.webhooks.configuration", "com.nimble.webhooks.entity","com.nimble.webhooks.helper", "com.nimble.webhooks.cors" })
@ImportResource("classpath:spring.xml")
@SpringBootApplication
@EnableAsync
public class Application extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}

}
