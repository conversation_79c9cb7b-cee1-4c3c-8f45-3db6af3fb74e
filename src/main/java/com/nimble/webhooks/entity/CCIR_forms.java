package com.nimble.webhooks.entity;

import java.util.Calendar;
import java.util.TimeZone;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "cd_ccir_forms")
public class CCIR_forms {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	
	private String ticket_number;
	private String email = "NA";
	private String customer_name = "NA";
	private String qrc = "NA";
	private String issues = "NA";
	private String ticket_status = "NA";
	private String device = "NA";
	private String old_iris_device = "NA";
	private String new_iris_device = "NA";
	private String cd_steps = "NA";
	private String product_steps = "NA";
	private String current_status = "NA";
	private String commands = "NA";
	private String product = "NA";

	@Temporal(TemporalType.TIMESTAMP)
	private Calendar registered = Calendar.getInstance(TimeZone.getTimeZone("UTC"));

	private String last_date = "1111-11-11";

	private boolean deleted_status;

	public CCIR_forms() {
		super();
	}

	public CCIR_forms(String ticket_number, String email, String customer_name, String qrc, String issues,
			String ticket_status, String device, String last_date, boolean deleted_status, String product,
			String old_iris_device, String registered) {
		super();
		this.ticket_number = ticket_number;
		this.email = email;
		this.customer_name = customer_name;
		this.qrc = qrc;
		this.issues = issues;
		this.ticket_status = ticket_status;
		this.device = device;
		this.last_date = last_date;
		this.deleted_status = deleted_status;
		this.product = product;
		this.old_iris_device = old_iris_device;
	}

	public String getLast_date() {
		return last_date;
	}

	public void setLast_date(String last_date) {
		this.last_date = last_date;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getTicket_number() {
		return ticket_number;
	}

	public void setTicket_number(String ticket_number) {
		this.ticket_number = ticket_number;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getCustomer_name() {
		return customer_name;
	}

	public void setCustomer_name(String customer_name) {
		this.customer_name = customer_name;
	}

	public String getQrc() {
		return qrc;
	}

	public void setQrc(String qrc) {
		this.qrc = qrc;
	}

	public String getIssues() {
		return issues;
	}

	public void setIssues(String issues) {
		this.issues = issues;
	}

	public String getTicket_status() {
		return ticket_status;
	}

	public void setTicket_status(String ticket_status) {
		this.ticket_status = ticket_status;
	}

	public Calendar getRegistered() {
		return registered;
	}

	public void setRegistered(Calendar registered) {
		this.registered = registered;
	}

	public boolean isDeleted_status() {
		return deleted_status;
	}

	public void setDeleted_status(boolean deleted_status) {
		this.deleted_status = deleted_status;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getOld_iris_device() {
		return old_iris_device;
	}

	public void setOld_iris_device(String old_iris_device) {
		this.old_iris_device = old_iris_device;
	}

	public String getCd_steps() {
		return cd_steps;
	}

	public void setCd_steps(String cd_steps) {
		this.cd_steps = cd_steps;
	}

	public String getProduct_steps() {
		return product_steps;
	}

	public void setProduct_steps(String product_steps) {
		this.product_steps = product_steps;
	}

	public String getCurrent_status() {
		return current_status;
	}

	public void setCurrent_status(String current_status) {
		this.current_status = current_status;
	}

	public String getCommands() {
		return commands;
	}

	public void setCommands(String commands) {
		this.commands = commands;
	}

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public String getNew_iris_device() {
		return new_iris_device;
	}

	public void setNew_iris_device(String new_iris_device) {
		this.new_iris_device = new_iris_device;
	}
}
