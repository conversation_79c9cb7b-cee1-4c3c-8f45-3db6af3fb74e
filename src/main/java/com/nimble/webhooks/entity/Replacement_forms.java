package com.nimble.webhooks.entity;

import java.util.Calendar;
import java.util.TimeZone;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "cd_replacement_form")
public class Replacement_forms {

	@Id
	@Column(name="r_id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;
	private String ticket_no;
	private String product_type;
	private String customer_name;
	private String email;
	private String phone;
	private String reason;
	private String old_iris_device;
	private String old_qrc;
	private String ticket_status;
	
//	@Temporal(TemporalType.TIMESTAMP)
	private String registered_date;

	private String device_registered_date;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Calendar modified_date = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
	private boolean deleted_status;
	private String order_id;
	private String order_date;
	
	public Replacement_forms() {
		super();
	}
	public Replacement_forms(String ticket_no, String product_type, String customer_name, String email,
			String phone, String reason, String old_iris_device, String old_qrc, String ticket_status,
			boolean deleted_status, String order_id, String order_date, String deviceRegisteredDate) {
		super();
		this.ticket_no = ticket_no;
		this.product_type = product_type;
		this.customer_name = customer_name;
		this.email = email;
		this.phone = phone;
		this.reason = reason;
		this.old_iris_device = old_iris_device;
		this.old_qrc = old_qrc;
		this.ticket_status = ticket_status;
		this.deleted_status = deleted_status;
		this.order_id = order_id;
		this.order_date = order_date;
		this.device_registered_date = deviceRegisteredDate;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getTicket_no() {
		return ticket_no;
	}
	public void setTicket_no(String ticket_no) {
		this.ticket_no = ticket_no;
	}
	public String getProduct_type() {
		return product_type;
	}
	public void setProduct_type(String product_type) {
		this.product_type = product_type;
	}
	public String getCustomer_name() {
		return customer_name;
	}
	public void setCustomer_name(String customer_name) {
		this.customer_name = customer_name;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getOld_iris_device() {
		return old_iris_device;
	}
	public void setOld_iris_device(String old_iris_device) {
		this.old_iris_device = old_iris_device;
	}
	public String getOld_qrc() {
		return old_qrc;
	}
	public void setOld_qrc(String old_qrc) {
		this.old_qrc = old_qrc;
	}
	public String getTicket_status() {
		return ticket_status;
	}
	public void setTicket_status(String ticket_status) {
		this.ticket_status = ticket_status;
	}
	public String getRegistered_date() {
		return registered_date;
	}
	public void setRegistered_date(String registered_date) {
		this.registered_date = registered_date;
	}
	public Calendar getModified_date() {
		return modified_date;
	}
	public void setModified_date(Calendar modified_date) {
		this.modified_date = modified_date;
	}
	public boolean isDeleted_status() {
		return deleted_status;
	}
	public void setDeleted_status(boolean deleted_status) {
		this.deleted_status = deleted_status;
	}

	public String getOrder_id() {
		return order_id;
	}

	public void setOrder_id(String order_id) {
		this.order_id = order_id;
	}

	public String getOrder_date() {
		return order_date;
	}

	public void setOrder_date(String order_date) {
		this.order_date = order_date;
	}

	public String getDevice_registered_date() {
		return device_registered_date;
	}

	public void setDevice_registered_date(String device_registered_date) {
		this.device_registered_date = device_registered_date;
	}
}
