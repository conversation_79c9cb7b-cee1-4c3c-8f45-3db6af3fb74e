package com.nimble.webhooks.service.impl;

import com.nimble.webhooks.dto.NiomObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.nimble.webhooks.dto.IrisObject;
import com.nimble.webhooks.entity.CCIR_forms;
import com.nimble.webhooks.entity.Replacement_forms;
import com.nimble.webhooks.irisentity.FreshDeskHooks;
import com.nimble.webhooks.service.IAsyncService;
import com.nimble.webhooks.service.IDataBaseService;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.TimeZone;

@Service
public class AsyncServiceImpl implements IAsyncService {

	private static final Logger log = LogManager.getLogger(AsyncServiceImpl.class);

	@Autowired
	IDataBaseService dataBaseService;

	@Override
//	@Async("asyncExecutor")
	public void ticketProcessor(String request, FreshDeskHooks freshDeskHooks) {
		log.info("ticketProcessor :");
		System.out.println("ticket");
		try {
			JSONObject response = new JSONObject(request);
			JSONObject res = response.getJSONObject("freshdesk_webhook");

			String ticket_id = res.getString("ticket_id");
			log.info("Ticket ID : " + ticket_id);

			String customer_email = ifNull(res.getString("ticket_contact_email")) ? "NA"
					: res.getString("ticket_contact_email");
			String customer_name = ifNull(res.getString("ticket_contact_name")) ? "NA"
					: res.getString("ticket_contact_name");
			String qrc = ifNull(res.getString("ticket_cf_qrc")) ? "NA" : res.getString("ticket_cf_qrc");
			String issue = ifNull(res.getString("ticket_cf_technical_issue")) ? "NA"
					: res.getString("ticket_cf_technical_issue");
			String ticket_status = ifNull(res.getString("ticket_status")) ? "NA" : res.getString("ticket_status");
			String ccir_db_status = ifNull(res.getString("ticket_cf_ccir_db_status")) ? "NA"
					: res.getString("ticket_cf_ccir_db_status");
			String product = ifNull(res.getString("ticket_cf_subscription_types")) ? "NA"
					: res.getString("ticket_cf_subscription_types");
			String phone = ifNull(res.getString("ticket_contact_phone")) ? "NA" : res.getString("ticket_contact_phone");
			String mobile = ifNull(res.getString("ticket_contact_mobile")) ? "NA"
					: res.getString("ticket_contact_mobile");

			String modelName = "NA";
			String orderId = "NA";
			String orderDate = "1111-11-11";
			String deviceRegisteredDate = "1970-01-01 00:00:00";
			String registeredDate = "1970-01-01 00:00:00";
			String lastReportedDate = "2019-01-01";
			IrisObject irisObject = dataBaseService.getIrisObject(qrc);
			if (irisObject != null){
				lastReportedDate = ifNull(irisObject.getLastReportDate()) ? "2019-01-01"
						: irisObject.getLastReportDate().split(" ")[0];
				modelName = ifNull(irisObject.getModel_name()) ? "NA" : irisObject.getModel_name();
				NiomObject niomObject = dataBaseService.getNiomObject(irisObject.getMeid());
				if(niomObject != null){
					orderId = ifNull(niomObject.getOrderId()) ? "NA" : niomObject.getOrderId();
					orderDate = ifNull(niomObject.getOrderDate()) ? "NA" : niomObject.getOrderDate();
					deviceRegisteredDate = ifNull(niomObject.getRegisteredDate()) ? "1753-01-01 00:00:00"
							: niomObject.getRegisteredDate().split(" ")[0];
				}
			}

			boolean status = false;
			if (ccir_db_status.equalsIgnoreCase("replacement")) {
				Replacement_forms replacement_form = new Replacement_forms(ticket_id, product, customer_name,
						customer_email, mobile.equals("NA") ? phone : mobile, issue, modelName, qrc, ccir_db_status,
						true, orderId, orderDate, deviceRegisteredDate);
				Replacement_forms old_replacement_form = dataBaseService.getReplacementForm(ticket_id);
				if (old_replacement_form != null) {
					replacement_form.setId(old_replacement_form.getId());
				} else {
					replacement_form.setRegistered_date(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Calendar.getInstance().getTime()));
				}
				status = dataBaseService.saveOrUpdateReplacementForm(replacement_form);
			} else {
				CCIR_forms ccir_form = new CCIR_forms(ticket_id, customer_email, customer_name, qrc, issue,
						ccir_db_status, "NA", lastReportedDate, true, product, modelName, deviceRegisteredDate);
				CCIR_forms old_ccir_form = dataBaseService.getCCIRForm(ticket_id);
				if (old_ccir_form != null) {
					ccir_form.setId(old_ccir_form.getId());
				}
				status = dataBaseService.saveOrUpdateCCIRForm(ccir_form);
			}

			if (status) {
				freshDeskHooks.setUpdate_status(true);
				dataBaseService.saveFreshDeshHooks(freshDeskHooks);
			}
		} catch (JSONException e) {
			log.error("JSON Error in ticketProcessor : " + e.getLocalizedMessage());
		} catch (Exception e) {
			log.error("Error in ticketProcessor : " + e.getLocalizedMessage());
		}
	}

	private boolean ifNull(String targetValue) {
		if (targetValue == null || targetValue.equals("null")) {
			return true;
		}
		return false;
	}
}
