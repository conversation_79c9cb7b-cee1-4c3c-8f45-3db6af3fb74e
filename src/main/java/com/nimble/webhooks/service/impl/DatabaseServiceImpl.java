package com.nimble.webhooks.service.impl;

import com.nimble.webhooks.dto.NiomObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.nimble.webhooks.dao.IDatabaseDao;
import com.nimble.webhooks.dto.IrisObject;
import com.nimble.webhooks.entity.CCIR_forms;
import com.nimble.webhooks.entity.Replacement_forms;
import com.nimble.webhooks.irisentity.FreshDeskHooks;
import com.nimble.webhooks.service.IDataBaseService;

@Service
@Transactional
public class DatabaseServiceImpl implements IDataBaseService {
	
	@Autowired
	IDatabaseDao databaseDao;

	@Override
	@Transactional("iristransactionManager")
	public IrisObject getIrisObject(String qrc) {
		return databaseDao.getIrisObject(qrc);
	}

	@Override
	public CCIR_forms getCCIRForm(String ticket_id) {
		return databaseDao.getCCIRForm(ticket_id);
	}

	@Override
	public boolean saveOrUpdateCCIRForm(CCIR_forms ccir_form) {
		return databaseDao.saveOrUpdateCCIRForm(ccir_form);
	}

	@Override
	public Replacement_forms getReplacementForm(String ticket_id) {
		return databaseDao.getReplacementForm(ticket_id);
	}

	@Override
	public boolean saveOrUpdateReplacementForm(Replacement_forms replacement_form) {
		return databaseDao.saveOrUpdateReplacementForm(replacement_form);
	}

	@Override
	@Transactional("iristransactionManager")
	public FreshDeskHooks webHookStatusIsAvailable(String ticket_id) {
		return databaseDao.webHookStatusIsAvailable(ticket_id);
	}

	@Override
	@Transactional("iristransactionManager")
	public FreshDeskHooks saveFreshDeshHooks(FreshDeskHooks freshDeskHooks) {
		return databaseDao.saveFreshDeshHooks(freshDeskHooks);
	}

	@Override
	@Transactional("niomtransactionManager")
	public NiomObject getNiomObject(String meid) {
		return databaseDao.getNiomObject(meid);
	}

}
