package com.nimble.webhooks.irisentity;

import java.util.Calendar;
import java.util.TimeZone;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "freshdesk_webhooks_status")
public class FreshDeskHooks {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private long id;
	private String ticket_number;
	private String technical_issue;
	private String ticket_status;
	private boolean update_status;
	private String triggered_event;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Calendar updated_date = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
	
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getTicket_number() {
		return ticket_number;
	}
	public void setTicket_number(String ticket_number) {
		this.ticket_number = ticket_number;
	}
	public String getTechnical_issue() {
		return technical_issue;
	}
	public void setTechnical_issue(String technical_issue) {
		this.technical_issue = technical_issue;
	}
	public String getTicket_status() {
		return ticket_status;
	}
	public void setTicket_status(String ticket_status) {
		this.ticket_status = ticket_status;
	}
	public boolean isUpdate_status() {
		return update_status;
	}
	public void setUpdate_status(boolean update_status) {
		this.update_status = update_status;
	}
	public Calendar getUpdated_date() {
		return updated_date;
	}
	public void setUpdated_date(Calendar updated_date) {
		this.updated_date = updated_date;
	}
	public String getTriggered_event() {
		return triggered_event;
	}
	public void setTriggered_event(String triggered_event) {
		this.triggered_event = triggered_event;
	}
}
