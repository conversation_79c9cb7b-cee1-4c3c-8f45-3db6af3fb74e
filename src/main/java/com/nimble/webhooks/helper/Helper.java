package com.nimble.webhooks.helper;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.nimble.webhooks.irisentity.FreshDeskHooks;

public class Helper {
	
	private static final Logger log = LogManager.getLogger(Helper.class);

	public static FreshDeskHooks setData(FreshDeskHooks freshDeskHooks, JSONObject res) {
		log.info("setData ::");
		try {
			if (freshDeskHooks == null) {
				freshDeskHooks = new FreshDeskHooks();
			}
			freshDeskHooks.setTicket_number(res.getString("ticket_id"));
			freshDeskHooks.setTechnical_issue(res.getString("ticket_cf_technical_issue"));
			freshDeskHooks.setTicket_status(res.getString("ticket_status"));
			freshDeskHooks.setTriggered_event(res.getString("triggered_event"));
			freshDeskHooks.setId(0);
			
		} catch (Exception e) {
			log.error("Handling freshdesk setData : "+e.getLocalizedMessage());
		}
		return freshDeskHooks;
	}
}
