package com.nimble.webhooks.dao.impl;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.ICheckserviceDao;
import com.nimble.webhooks.entity.Testtable;

@Repository
public class CheckserviceDaoImpl implements ICheckserviceDao {
	private static final Logger log = LogManager.getLogger(CheckserviceDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Override
	public Testtable DBSaveOrUpdatequery(Testtable testtable) {
		try {
			Session session = sessionFactory.getCurrentSession();
			session.merge(testtable);
			return testtable;
		} catch (Exception e) {
			log.error("Error in DBSaveOrUpdatequery: "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public Testtable DBDeletequery(Testtable testtable) {
		try {
			Session session = sessionFactory.getCurrentSession();
			session.delete(testtable);
			return testtable;
		} catch (Exception e) {
			log.error("Error in DBDeletequery: "+e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public Testtable DBselectquery(Testtable testtable) {
		try {
			Session session = sessionFactory.getCurrentSession();
			testtable=(Testtable) session.createQuery("from Testtable where check_service='"+testtable.getCheckServer()+"' and  server_ip = '"+testtable.getServerIp()+"'").list().get(0);
			return testtable;
		} catch (Exception e) {
			log.error("Error in DBselectquery: "+e.getLocalizedMessage());
		}
		return null;
	}

}
