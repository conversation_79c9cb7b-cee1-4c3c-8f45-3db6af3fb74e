package com.nimble.webhooks.dao.impl;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;

import com.nimble.webhooks.dto.NiomObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.nimble.webhooks.dao.IDatabaseDao;
import com.nimble.webhooks.dto.IrisObject;
import com.nimble.webhooks.entity.CCIR_forms;
import com.nimble.webhooks.entity.Replacement_forms;
import com.nimble.webhooks.irisentity.FreshDeskHooks;

@Repository
public class DatabaseDaoImpl implements IDatabaseDao {

	private static final Logger log = LogManager.getLogger(DatabaseDaoImpl.class);

	@Autowired
	private SessionFactory sessionFactory;

	@Autowired
	private SessionFactory irisSessionFactory;
	
	@Autowired
	private SessionFactory niomSessionFactory;

	@Override
	public IrisObject getIrisObject(String qrc) {
		log.info("getIrisObject qrc:" + qrc);
		try {
			String query = "SELECT LGR.`datetime`,AM.model,G.meid FROM gateway G JOIN assetmodel AM ON AM.id=G.model_id "
					+ "LEFT JOIN lastgatewayreport LGR ON G.id=LGR.gateway_id "
					+ "where G.qrcode=:qrc ;";
			List<Object[]> lastGatewayRptList = irisSessionFactory.getCurrentSession().createSQLQuery(query)
					.setParameter("qrc", qrc).list();
			if (!lastGatewayRptList.isEmpty()) {
				IrisObject irisObject = new IrisObject();
				Object[] obj = lastGatewayRptList.get(0);
				irisObject.setLastReportDate(((Timestamp) obj[0]).toString());
				irisObject.setModel_name((String) obj[1]);
				irisObject.setMeid((String) obj[2]);
				return irisObject;
			}
		} catch (Exception e) {
			log.error("Error in getIrisObject : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public boolean saveOrUpdateCCIRForm(CCIR_forms ccir_form) {
		log.info("saveOrUpdateCCIRForm ::");
		try {
			sessionFactory.getCurrentSession().merge(ccir_form);
			log.info("Updated successfully");
			return true;
		} catch (Exception e) {
			log.error("Error saveOrUpdateCCIRForm : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public boolean saveOrUpdateReplacementForm(Replacement_forms replacement_form) {
		log.info("saveOrUpdateReplacementForm ::");
		try {
			sessionFactory.getCurrentSession().merge(replacement_form);
			log.info("Updated successfully");
			return true;
		} catch (Exception e) {
			log.error("Error in saveOrUpdateReplacementForm : " + e.getLocalizedMessage());
		}
		return false;
	}

	@Override
	public CCIR_forms getCCIRForm(String ticket_id) {
		log.info("getCCIRForm : " + ticket_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			CriteriaBuilder builder = session.getCriteriaBuilder();
			CriteriaQuery<CCIR_forms> criteria = builder.createQuery(CCIR_forms.class);
			Root<CCIR_forms> postRoot = criteria.from(CCIR_forms.class);

			criteria.select(postRoot).where(builder.equal(postRoot.get("ticket_number"), ticket_id));
			TypedQuery<CCIR_forms> qry = session.createQuery(criteria);
			List<CCIR_forms> result = qry.getResultList();
			if (!result.isEmpty()) {
				return result.get(0);
			}
		} catch (Exception e) {
			log.error("Error in getCCIRForm : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public Replacement_forms getReplacementForm(String ticket_id) {
		log.info("getReplacementForm : " + ticket_id);
		try {
			Session session = sessionFactory.getCurrentSession();
			CriteriaBuilder builder = session.getCriteriaBuilder();
			CriteriaQuery<Replacement_forms> criteria = builder.createQuery(Replacement_forms.class);
			Root<Replacement_forms> postRoot = criteria.from(Replacement_forms.class);

			criteria.select(postRoot).where(builder.equal(postRoot.get("ticket_no"), ticket_id));
			TypedQuery<Replacement_forms> qry = session.createQuery(criteria);
			List<Replacement_forms> result = qry.getResultList();
			if (!result.isEmpty()) {
				return result.get(0);
			}
		} catch (Exception e) {
			log.error("Error in getReplacementForm : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public FreshDeskHooks webHookStatusIsAvailable(String ticket_id) {
		log.info("webHookStatusIsAvailable : "+ ticket_id);
		try {
			Session session = irisSessionFactory.getCurrentSession();
			CriteriaBuilder builder = session.getCriteriaBuilder();
			CriteriaQuery<FreshDeskHooks> criteria = builder.createQuery(FreshDeskHooks.class);
			Root<FreshDeskHooks> postRoot = criteria.from(FreshDeskHooks.class);

			criteria.select(postRoot).where(builder.equal(postRoot.get("ticket_number"), ticket_id));
			TypedQuery<FreshDeskHooks> qry = session.createQuery(criteria);
			List<FreshDeskHooks> result = qry.getResultList();
			if (!result.isEmpty()) {
				return result.get(0);
			}
		} catch (Exception e) {
			log.error("Error in webHookStatusIsAvailable : " + e.getLocalizedMessage());
		}
		return null;
	}

	@Override
	public FreshDeskHooks saveFreshDeshHooks(FreshDeskHooks freshDeskHooks) {
		log.info("saveFreshDeshHooks ::");
		try {
			freshDeskHooks = (FreshDeskHooks) irisSessionFactory.getCurrentSession().merge(freshDeskHooks);
			log.info("Updated successfully");
		} catch (Exception e) {
			log.error("Error in saveFreshDeshHooks : " + e.getLocalizedMessage());
		}
		return freshDeskHooks;
	}

	@Override
	public NiomObject getNiomObject(String meid) {
		log.info("getNiomObject meid:" + meid);
		try {
			String query = "SELECT O.order_id,O.datetime,OM.registered_date "
					+ "FROM orders O "
					+ "JOIN ordermap OM ON O.order_id=OM.order_id "
					+ "where OM.meid=:meid ;";
			List<Object[]> orderList = niomSessionFactory.getCurrentSession().createSQLQuery(query)
					.setParameter("meid", meid).list();
			if (!orderList.isEmpty()) {
				NiomObject niomObject = new NiomObject();
				Object[] obj = orderList.get(0);
				niomObject.setOrderId(((BigInteger) obj[0]).toString());
				niomObject.setOrderDate(((Timestamp) obj[1]).toString());
				niomObject.setRegisteredDate(((Timestamp) obj[2]).toString());
				return niomObject;
			}
		} catch (Exception e) {
			log.error("Error in getNiomObject : " + e.getLocalizedMessage());
		}
		return null;
	}
}
