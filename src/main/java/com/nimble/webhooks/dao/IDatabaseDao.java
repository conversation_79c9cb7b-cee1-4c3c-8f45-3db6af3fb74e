package com.nimble.webhooks.dao;

import com.nimble.webhooks.dto.IrisObject;
import com.nimble.webhooks.dto.NiomObject;
import com.nimble.webhooks.entity.CCIR_forms;
import com.nimble.webhooks.entity.Replacement_forms;
import com.nimble.webhooks.irisentity.FreshDeskHooks;

public interface IDatabaseDao {

	IrisObject getIrisObject(String qrc);

	CCIR_forms getCCIRForm(String ticket_id);

	boolean saveOrUpdateCCIRForm(CCIR_forms ccir_form);

	Replacement_forms getReplacementForm(String ticket_id);

	boolean saveOrUpdateReplacementForm(Replacement_forms replacement_form);

	FreshDeskHooks webHookStatusIsAvailable(String ticket_id);

	FreshDeskHooks saveFreshDeshHooks(FreshDeskHooks freshDeskHooks);

	NiomObject getNiomObject(String meid);

}
