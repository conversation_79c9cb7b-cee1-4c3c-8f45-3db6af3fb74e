<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
	<Appenders>
		<!-- <RollingRandomAccessFile name="ApplicationLogger" fileName="/opt/log/irisservicesv4/irisservicesv4.log"
			immediateFlush="true" filePattern="/opt/log/irisservicesv4/irisservicesv4.log-%d{yyyy-MM-dd-HH-mm-ss}.log"
			> -->
		<RollingRandomAccessFile
			name="ApplicationLogger"
			fileName="/opt/log/freshdeskhooks/freshdeskhooks.log"
			immediateFlush="true"
			filePattern="/opt/log/freshdeskhooks/freshdeskhooks.log-%d{yyyy-MM-dd-HH-mm-ss}.log">
			<PatternLayout>
				<pattern>%d|[%t]|%p|%c{1}:%L|%M|%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="2 MB" />
			</Policies>
			<DefaultRolloverStrategy max="5" />
		</RollingRandomAccessFile>
		<!-- <RollingRandomAccessFile name="AdvanceLogger"
			fileName="/opt/development/log/irisservices/AdvancedLog/Set_1/API_TransactionDetailsReport_Set1.csv"
			immediateFlush="true"
			filePattern="/opt/development/log/irisservices/AdvancedLog/Set_1/API_TransactionDetailsReport_Set1-%d{yyyy-MM-dd-HH-mm-ssyyyy-MM-dd-HH-mm-ss}.csv">
			<PatternLayout>
				<header>ServiceURL,HttpMethod,APIName,AuthKey,AuthValidationTiming,GetUserIDTiming,GETNUMBERELAP,GetGeneralElapTime,getLatestReferEarnElapTime,getExternalConfigValueElapTime,getUserByName,getUserByEmail,updateLastLoginTypeAndTime,loginViaGoogleOrFacebook,getSignTypeElapTime,getUserBySignUpTokenElapTime,signUpElapTime,updateUserCompleteSetUp,getZipCodeDetails,saveZipCode,updateUser,createEmailVerificationToken,sendVerificationMail,getUserById,UsertokenElapTime,SaveOrUpdateUserTokenElapTime,getUserByNameV2ElapTime,getAssetSummaryTestElapTime,getCompanyConfigServiceElapTime,getLastgatewayreportElapTime,GetLstGatrptElapTime,LastGatewayReportsElapTime,LastGatewayElapTime,LastGroupsElapTime,LastAssetinformationElapTime,getAssetDescriptionElapTime,getGetGatewaySummaryTestV4ElapTime,getUserId_cmpIdByAuthElapTime,getLastgatewayreportV4ElapTime,getCompanyConfigController,getGatewaysElapTime,getURLElapTime,getCompanyConfigV4ElapTime,verifyAuthKeyV2ElapTime,getCompanyConfigAndCompanyElaptime,isMeidMappedInOrdermapElapTime,getAlertCfgByIdElapTime,getAlertCfgElapTime,getGatewayElapTime,getAlertCfgByIdV4ElapTime,getAlertCfgV4ElapTime,getGatewayConfigElapTime,getCurrentSubscriptionPlanElapTime,getSubscriptionElapTime,getChargebeePlanByIdElapTime,getPlanAndPeriodElapTime,getSubsPlanByIdElapTime,checkDeviceConfigStatusElapTime,getGatewayByUserElapTime,getGatewaysByReportTime,getFurBitDailyReportElapTime,getGatewayByMonitorTypeElapTime,getFurBitDailyReportServiceElapTime,getFurBitBatteryLifeElapTime,getPetProfileElapTime,getPetSafetyUrlsElapTime,getRvPetSafetyBlogUrlElapTime,getadvertisementinfoElapTime,getAdvertismentUrlElapTime,enabledisablealertcfgElapTime,getApiElpaTime,getCmpAccountElapTime,updateElapTime,saveOrUpdateCmpAccountElapTime,getAssetSummaryv3ElapTime,getProbeCategoryElapTime,getDeviceSubscriptionElapTime,getNotificaitonByUserIDElapTime,userNotificationsElapTime,getUserNotificationStatusElapTime,updateUserNotificationElapTime,generateReferralLinkElapTime,getLatestReferralCreditsElapTime,getTrendingVideosListElapTime,getTrendingvideosElapTime,getTrendingvideoInfoElapTime,AlertCFG_POST_saveORupdateGatewayByIdElapTime,SaveCompanyConfigElapTime,UpdateCompanyCfgElapTime,StartTime,EndTime,ElapsedTime${sys:line.separator}
				</header>
				<pattern>%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="5 MB" />
			</Policies>
			<DefaultRolloverStrategy max="250" />
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="AdvanceLogger2"
			fileName="/opt/development/log/irisservices/AdvancedLog/Set_2/API_TransactionDetailsReport_Set2.csv"
			immediateFlush="true"
			filePattern="/opt/development/log/irisservices/AdvancedLog/Set_2/API_TransactionDetailsReport_Set2-%d{yyyy-MM-dd-HH-mm-ssyyyy-MM-dd-HH-mm-ss}.csv">
			<PatternLayout>
				<header>ServiceURL,HttpMethod,APIName,AuthKey,AuthValidationTiming,getOrderChannelControllerElapTime,getOrderChannelServiceElapTime,checkWifiStatusV2ElapTime,isAlreadycontainElapTime,getGetFurBitBatteryLifeElapTime,getFurBitlastreportElapTime,getAvailableUpgradeSubscriptionPlanElapTime,getAvailUpgradePlansElapTime,getRemainingDaysElapTime,getAlertSummaryElapTime,getGetCompanyConfigServiceElapTime,getackalertsElapTime,getAssetDescriptionElapTime,getCompanyByIdElapTime,getCompanyElapTime,getAlertTypeElapTime,getAlertTypesElapTime,getPetProfile_ElapTime,getJPetprofilesByUserElapTime,getUserLeaderboardDetails_c_ElapTime,getUserLeaderboardDetails_s_ElapTime,getLeaderboardDetails_C_ElepTime,GetGatewayByUserElapTime,getLeaderBoardDetails_S_ElepTime,updateGoalSetting_C_ElepTime,updateGoalSetting_S_ElepTime,getSpeciesElapTime,getPetSpeciesElapTime,getBreedsElapTime,getAllPetBreedsElapTime,getPetSpeciesByNameElapTime,getPetBreedsElapTime,getZipcodeElapTime,getGetZipCodeDetailsElapTime,getSaveZipCodeElapTime,userUpdateElapTime,getUserByEmailElapTime,getUserByNameElapTime,updateUserElapTime,passwordUpdate,isForceUpdateElapTime,getUserByUsernameV2ElapTime,getSubscriptionPlanByMonitortypeElapTime,getUserById,getPlanAndPeriodElapTime,getAvailUpgradePlanNewElapTime,updateSubscriptionPlanElapTime,getChargebeePlanByIdElapTime,getOrderMappingByUserElapTime,getCreditAmountBySKUElapTime,resendVerificationLinkElapTime,sendVerificationMailElapTime,getUserInRoleElapTime,userdeviceinfoElapTime,saveOrUpdateUserDeviceInfoElapTime,activateUserElapTime,getAssetModelByNameElapTime,getGroupsElapTime,gatewayExitsinDBElapTime,saveORupdateQRCGatewayElapTime,saveORupdateAlertCfgElapTime,createUserInChargebeeElapTime,saveOrderMappingDetailsElapTime,saveorupdateofflineUserDetailsElapTime,updateRegisterUserEmailStatusElapTime,GetTemperature_CElapTime,SendEmailControllerElapTime,SendEmailServiceElapTime,configureDeviceElapTime,GetFurBitLastGatewayReportElapTime,GetFLastGatewayReportByUserElapTime,ConvertJFurBitLastGatewayReportElapTime,GetFurBitReportControllerElapTime,GetGatewayElapTime,GetFurBitReportServiceElapTime,StartTime,EndTime,ElapsedTime${sys:line.separator}
				</header>
				<pattern>%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="5 MB" />
			</Policies>
			<DefaultRolloverStrategy max="250" />
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="AdvanceLogger3"
			fileName="/opt/development/log/irisservices/AdvancedLog/Set_3/API_TransactionDetailsReport_Set3.csv"
			immediateFlush="true"
			filePattern="/opt/development/log/irisservices/AdvancedLog/Set_3/API_TransactionDetailsReport_Set3-%d{yyyy-MM-dd-HH-mm-ssyyyy-MM-dd-HH-mm-ss}.csv">
			<PatternLayout>
				<header>ServiceURL,HttpMethod,APIName,AuthKey,AuthValidationTiming,getUserId_cmpIdByAuthElapTime,getCompanyById_V4_C_ElapTime,getCompanyElapTime,getAlertV3_V4ELapTimes,GetackalertsElapTime,GetAssetDescriptionElapTime,getGeneraldataV4ElapTime,getLatestReferEarnV4ElapTime,generateReferralLinkV4ElapTime,getEmailByAuthElapTime,getLatestReferralCreditsElapTime,getUserV2_V4ElapTime,getGetUserById_CmpIdElapTime,getPetProfileV4ElapTime,getJPetprofilesByUserV4ElapTime,getTrendingVideosListV4ElapTime,getTrendingvideoInfoserviceV4ElapTime,updateYoutubeStatisticsElapTime,getOrderChannelV4ElapTime,getOrderChannelV4ServiceElapTime,getBreedsV4ElapTime,getBreedsV4ServiceElapTime,usertokenV4ElapTime,saveOrUpdateUserTokenV4ElapTime,getUserByUsernameV4ElapTime,getUserByNameV4ElapTime,passwordUpdateV4ElapTime,updateUserV4ElapTime,LoginV4ElapTime,GetUserByUsernameV4forloginElapTime,getUpdateGoalSettingsV4ElapTime,getUpdateGoalSetting_S_ElepTime,getGetNotificaitonByUserIDV4ElapTime,getUserNotificationsV4ElapTime,getGetUserNotificationStatusV4ElapTime,getUpdateUserNotificationElapTime,userUpdateV4ElapTime,getUserId_cmpIdByAuthV4ElapTime,getUserByUNameOrEmailV4ElapTime,updateUserV4byuseridbyuseridV4forloginElapTime,updateVideoStatusV4V4ElapTime,getUpdateVideoInfoTransactionV4ElapTime,getCreateVideoInfoTransactionV4ElapTime,getAlertType_V4ElapTime,getAlertTypesV4Elaptime,UserDeviceInfoV4ElapTime,SaveOrUpdateUserDeviceInfoV4,UpdateCompanyCfgV4,GetCompanyConfigAndCompany,isMeidMappedInOrdermap,VerifyAuthV3ElapTime,VerifyAuthV4ElapTime,updateAlertcfgControllerV4ElapTime,enabledisablealertcfgV4ElapTime,updateEmailPhoneV4ElapTime,updateNotifyV4ElapTime,updateAlertCfgServiceV4ElapTime,getGetUnackAlertsV4ElapTime,getGetAlertCfgV4ControllerV4ElapTime,saveorupdatePetProfileV4ControllerElapTime,saveorupdatePetProfileV4ServiceElapTime,updateGatewayNameElapTime,getGetUserId_cmpIdByAuthV2ElapTime,GetGatewayElapTime,GetPetSpeciesByNameElapTime,GetGatewayV4ElapTime,getGetadvertisementinfoV4ElapTime,getGetSpeciesV4ElapTime,StartTime,EndTime,ElapsedTime${sys:line.separator}
				</header>
				<pattern>%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="5 MB" />
			</Policies>
			<DefaultRolloverStrategy max="250" />
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="AdvanceLogger4"
			fileName="/opt/development/log/irisservices/AdvancedLog/Set_4/API_TransactionDetailsReport_Set4.csv"
			immediateFlush="true"
			filePattern="/opt/development/log/irisservices/AdvancedLog/Set_4/API_TransactionDetailsReport_Set4-%d{yyyy-MM-dd-HH-mm-ssyyyy-MM-dd-HH-mm-ss}.csv">
			<PatternLayout>
				<header>ServiceURL,HttpMethod,APIName,AuthKey,AuthValidationTiming,VerifyAuthV3ElapTime,VerifyAuthV3ElapTime,getCheckQrcExistInDbElapTime,getExternalQrcActivationStatusElapTime,getCheckQrcExistElapTime,getSaveWifiInfoElapTime,getGetGatewayAndUserDetailsElapTime,getIsAlreadycontainElapTime,getSaveOrUpdateWifiInfoElapTime,getGetWifiinfoListElapTime,getGetWifiListElapTime,getForgetPasswordElapTime,getSendForgotPasswordMailElapTime,StartTime,EndTime,ElapsedTime${sys:line.separator}
				</header>
				<pattern>%m%n</pattern>
			</PatternLayout>
			<Policies>
				<SizeBasedTriggeringPolicy size="5 MB" />
			</Policies>
			<DefaultRolloverStrategy max="250" />
		</RollingRandomAccessFile> -->
	</Appenders>
	<Loggers>
		<Logger name="ApplicationLogger" level="INFO"
			includeLocation="false" additivity="false">
			<appender-ref ref="ApplicationLogger" level="INFO" />
		</Logger>
		<!-- <Logger name="AdvanceLogger" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger2" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger2" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger3" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger3" level="INFO" />
		</Logger>
		<Logger name="AdvanceLogger4" level="INFO"
			includeLocation="true" additivity="false">
			<appender-ref ref="AdvanceLogger4" level="INFO" />
		</Logger> -->
		<Root level="INFO" includeLocation="false" additivity="false">
			<AppenderRef ref="ApplicationLogger" />
		</Root>
	</Loggers>
</Configuration>
